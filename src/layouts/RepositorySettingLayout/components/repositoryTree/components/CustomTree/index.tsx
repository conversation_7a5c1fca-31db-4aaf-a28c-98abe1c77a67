import type { TreeDataNode, TreeProps as AntdTreeProps } from 'antd';
import { Form, Input, message, Tree, Typography } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';

import { rename } from '@/api/File';
import { getRepositoryTree, moveFile } from '@/api/repository';
import type { FileItem } from '@/constants/fileList.config';
import useRepositoryAuth from '@/hooks/useRepositoryAuth';
import { useUrlQuery } from '@/hooks/useSearchParams';
import type { FileRecord } from '@/model/Desktop';
import type { DataNode } from '@/model/repository';
import type { TreeNode, TreeProps } from '@/model/repositoryTree';
import { useFormatMessage } from '@/modules/Locale';
import useTreeStore from '@/store/repositoryTree';
import { getFileIcon } from '@/utils/file';

import CreateFileBtn from '../../../CreateFileBtn';
import NodeMoreDropdown from '../../../NodeMoreDropdown';
import { transformToDataNode } from '../../ulits';

interface ITreeProps extends TreeProps {
  handleNodeMoreAction: (action: string, nodeData?: FileRecord, fileItem?: FileItem) => void;
  onTitleClick: (node?: FileRecord) => void;
}

const TreeComponent: React.FC<ITreeProps> = ({
  data = [],
  onNodeSelect,
  onNodeExpand,
  onNodeLoad,
  showIcon = true,
  checkable = false,
  selectable = true,
  multiple = false,
  defaultExpandAll = false,
  defaultExpandedKeys = [],
  defaultSelectedKeys = [],
  className,
  style,
  handleNodeMoreAction,
  onTitleClick,
}) => {
  const {
    treeData,
    selectedKeys,
    expandedKeys,
    setTreeData,
    addNode,
    updateNode,
    moveNode,
    findNode,
    setSelectedKeys,
    setExpandedKeys,
    setLoadingKey,
    setSelectedNode,
    setSelectedNodeRecord,
    setIsTopLevel,
  } = useTreeStore();

  const { hasPermission } = useRepositoryAuth();

  // 获取查询参数
  const queryParams = useUrlQuery();
  const { repositoryGuid = '' } = queryParams;

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const s18nText = {
    moveSuccess: useFormatMessage('Repository.moveSuccess'),
    moveFailed: useFormatMessage('Repository.moveFailed'),
    renameEmptyError: useFormatMessage('Repository.renameEmptyError'),
    renameFailed: useFormatMessage('Repository.renameFailed'),
    renameSuccess: useFormatMessage('Repository.renameSuccess'),
  };

  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [form] = Form.useForm();

  // 初始化数据
  useEffect(() => {
    if (treeData.length > 0) {
      return;
    }
    if (data.length > 0) {
      setTreeData(data);
    }
  }, [data, setTreeData, treeData.length]);

  // 初始化展开和选中状态
  useEffect(() => {
    if (defaultExpandedKeys.length > 0) {
      setExpandedKeys(defaultExpandedKeys);
    }
    if (defaultSelectedKeys.length > 0) {
      setSelectedKeys(defaultSelectedKeys);
    }
  }, [defaultExpandedKeys, defaultSelectedKeys, setExpandedKeys, setSelectedKeys]);

  // 处理节点选择
  const handleSelect: AntdTreeProps['onSelect'] = (keys, info) => {
    setSelectedNode(info.node as TreeNode);
    setSelectedKeys(keys as string[]);
    onNodeSelect?.(keys as string[], info);
    setIsTopLevel(false);
  };

  // 处理节点展开
  const handleExpand: AntdTreeProps['onExpand'] = (keys, info) => {
    setExpandedKeys(keys as string[]);
    onNodeExpand?.(keys as string[], info);
  };

  // 处理拖拽
  const handleDrop: AntdTreeProps['onDrop'] = async (info) => {
    const dropKey = info.node.key as string;
    const dragKey = info.dragNode.key as string;
    const dropPos = info.node.pos.split('-');
    const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);

    try {
      // 找到拖拽的节点
      const dragNode = findNode(dragKey);
      if (!dragNode) {
        message.error(s18nText.moveFailed);
        return;
      }

      // 找到目标节点
      const dropNode = findNode(dropKey);
      if (!dropNode) {
        message.error(s18nText.moveFailed);
        return;
      }

      // 移动成功后展开父节点
      const targetParentKey = info.dropToGap ? dropNode.parentKey || dragNode.parentKey || repositoryGuid : dropKey;

      // 构建API参数
      const moveParams = {
        fileGuid: dragKey, // 被移动的文件
        toFileGuid: info.dropToGap ? dropNode.parentKey || dragNode.parentKey || repositoryGuid : dropKey, // 移动后的父级文件
        afterFileGuid: info.dropToGap
          ? dropPosition === -1
            ? dragNode.parentKey || repositoryGuid
            : dropKey
          : dragNode.parentKey || repositoryGuid, // 在哪个文件后面
      };

      // 调用API
      const response = await moveFile(moveParams);

      if (response.status === 200) {
        // 本地移动
        moveNode(dragKey, dropKey, dropPosition, info.dropToGap);

        if (targetParentKey && !expandedKeys.includes(targetParentKey)) {
          const newExpandedKeys = [...expandedKeys, targetParentKey];
          setExpandedKeys(newExpandedKeys);
        }

        message.success(s18nText.moveSuccess);
      } else {
        message.error(s18nText.moveFailed);
      }
    } catch (error) {
      message.error(s18nText.moveFailed);
    }
  };

  // 处理动态加载
  const handleLoadData = useCallback(
    async (node: TreeDataNode) => {
      if (!onNodeLoad) return;

      const treeNode = node as TreeNode;
      setLoadingKey(treeNode.key, true);

      try {
        const res = await getRepositoryTree(treeNode.key);
        if (res?.status === 200) {
          const children = res?.data?.list || [];
          const childrenNodes = transformToDataNode(children);
          if (childrenNodes.length > 0) {
            childrenNodes.forEach((child: DataNode) => {
              addNode(treeNode.key, child);
            });
          }
        }
      } catch (error) {
      } finally {
        setLoadingKey(treeNode.key, false);
      }
    },
    [onNodeLoad, setLoadingKey, addNode],
  );

  // 开始编辑
  const startEdit = useCallback(
    (node: FileRecord) => {
      setEditingKey(node.guid);
      form.setFieldsValue({ [node.guid]: node.name });
    },
    [form],
  );

  // 保存编辑
  const saveEdit = useCallback(async () => {
    if (!editingKey) {
      message.error(s18nText.renameEmptyError);
      return;
    }

    try {
      const values = await form.validateFields();
      const newName = values[editingKey]?.trim();

      if (!newName) {
        message.error(s18nText.renameEmptyError);
        return;
      }

      const res = await rename(editingKey, newName);
      if (res.status === 200) {
        updateNode(editingKey, { title: newName });
        setEditingKey(null);
        form.resetFields();
        message.success(s18nText.renameSuccess);
      } else {
        message.error(s18nText.renameFailed);
      }
    } catch (error) {
      // 表单验证失败或其他错误
    }
  }, [editingKey, form, updateNode, s18nText]);

  // 取消编辑
  const cancelEdit = useCallback(() => {
    setEditingKey(null);
    form.resetFields();
  }, [form]);

  // 渲染节点标题
  const renderTitle = (node: DataNode) => {
    if (editingKey === node.key) {
      return (
        <Form form={form} style={{ display: 'inline-block' }}>
          <Form.Item
            name={node.key}
            rules={[
              { required: true, message: '请输入文件名' },
              { max: 255, message: '文件名不能超过255个字符' },
            ]}
            style={{ margin: 0 }}
          >
            <Input
              autoFocus
              size="small"
              style={{ width: 200 }}
              onBlur={saveEdit}
              onKeyDown={(e) => {
                if (e.key === 'Escape') {
                  cancelEdit();
                }
              }}
              onPressEnter={saveEdit}
            />
          </Form.Item>
        </Form>
      );
    }

    const handleAction = (action: string, nodeData?: FileRecord) => {
      if (action === 'rename') {
        startEdit(nodeData as FileRecord);
      } else {
        handleNodeMoreAction(action, nodeData);
      }
    };

    return (
      <div
        style={{
          display: 'inline-flex',
          alignItems: 'center',
          width: '100%',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          minWidth: 0,
        }}
      >
        <div style={{ display: 'flex' }} onClick={() => setSelectedNodeRecord(node?.record)}>
          <Typography.Text ellipsis={{ tooltip: true }} onClick={() => onTitleClick(node?.record)}>
            {node.title}
          </Typography.Text>
          {hasPermission(['canEditFile']) && (
            <>
              <CreateFileBtn
                onClick={(fileItem: FileItem) => {
                  handleNodeMoreAction('create', node?.record, fileItem);
                }}
              />
              <NodeMoreDropdown nodeData={node?.record} onAction={handleAction} />
            </>
          )}
        </div>
      </div>
    );
  };

  // 转换数据格式
  const convertToAntdTreeData = (nodes: DataNode[]): TreeDataNode[] => {
    return nodes.map((node) => ({
      key: node.key,
      title: renderTitle(node),
      children: node.children ? convertToAntdTreeData(node.children) : undefined,
      isLeaf: node.children ? node.children.length === 0 : node.isLeaf,
      icon: <img src={getFileIcon({ type: node?.record?.type || '' })} style={{ width: 20, marginRight: 8 }} />,
      style: { whiteSpace: 'nowrap' },
      parentKey: node?.parentKey || node?.record?.parentGuid,
    }));
  };

  return (
    <div className={className} style={{ height: 'calc(100vh - 285px)', ...style }}>
      <Tree
        checkable={checkable}
        defaultExpandAll={defaultExpandAll}
        draggable={{ icon: false }}
        expandedKeys={expandedKeys}
        loadData={onNodeLoad ? handleLoadData : undefined}
        multiple={multiple}
        selectable={selectable}
        selectedKeys={selectedKeys}
        showIcon={showIcon}
        style={{
          height: 'calc(100vh - 270px)',
          overflow: 'auto',
          whiteSpace: 'nowrap',
        }}
        treeData={convertToAntdTreeData(treeData)}
        onDrop={handleDrop}
        onExpand={handleExpand}
        onSelect={handleSelect}
      />
    </div>
  );
};

export default TreeComponent;
