import { Popover, Tooltip } from 'antd';
import classNames from 'classnames';
import React, { useEffect, useMemo, useState } from 'react';

import arrowRight from '@/assets/images/common/<EMAIL>';
import checkOutlined from '@/assets/images/common/<EMAIL>';
import { UserFeature } from '@/model/UserPermission';
import { fm } from '@/modules/Locale';
import { moDownTypes } from '@/pages/pc/Desktop/utils';
import { FileIconType } from '@/pages/pc/Enterprise/common/components/FileIcon/type';
import { useUserCheckPoint } from '@/service';
import { createFileAddToKnowledgeBaseConfigItemForEditorHeader } from '@/utils/knowledgeBase/fileAddToKnowledgeBase';

import css from './style.less';

interface PopoverProps {
  onItemClick?: (item: { key: string; exportType?: string; fileGuid?: string; noSupport?: boolean }) => void;
  onOpenInNewTab?: (key: string) => void;
  fileType?: string;
  isFavorite?: boolean;
  permissionsAndReasons: { [key: string]: any } | undefined; // 添加这一行
  isAdmin?: boolean;
  quickAccessAdded?: boolean;
  subscribed?: boolean;
  showComment?: boolean;
  showMenu?: boolean;
  showWriter?: boolean;
  /** 是否公开分享 */
  isPublicShare?: boolean;
  /** 是否协作者 */
  isCollaborators?: boolean;
}

export interface MenuItem {
  key: string;
  title: string;
  icon?: React.ReactNode;
  subItems?: MenuItem[];
  showDivider?: boolean;
  exportType?: string;
  disabled?: boolean;
  noSupport?: boolean;
  hide?: boolean;
  disabledTip?: string;
  active?: boolean;
}

export function FileMenuPopover({
  onItemClick,
  fileType = '',
  isFavorite = false,
  quickAccessAdded = false,
  subscribed = false,
  showComment = false,
  showMenu = false,
  showWriter = false,
  permissionsAndReasons,
  isPublicShare,
  isCollaborators,
}: PopoverProps) {
  // 新增子菜单弹出状态管理
  const [subMenuVisible, setSubMenuVisible] = useState<Record<string, boolean>>({});
  const { isFeaturePoint } = useUserCheckPoint();

  const getUnCollaborator = (list: MenuItem[], unCollaboratorConfigs: string[] = []) => {
    const arr = unCollaboratorConfigs.length
      ? unCollaboratorConfigs
      : [
          'addToQuickAccess',
          'subscribeUpdate',
          'favorite',
          'move',
          'shortcut',
          'lockFile',
          'createCopy',
          'download',
          'print',
          'helpCenter',
          'downToExcel',
          'saveVersion',
          'viewHistory',
          'addComment',
          'viewComment',
          'help',
        ];
    const keyMap = new Map();
    list.forEach((item) => keyMap.set(item.key!, item));
    return arr.map((key) => keyMap.get(key)).filter((item) => item !== undefined);
  };

  const canDownload = useMemo(() => {
    const result = moDownTypes.includes(fileType)
      ? permissionsAndReasons?.canExport.value
      : permissionsAndReasons?.canDownload.value;

    return result;
  }, [permissionsAndReasons, fileType]);

  // 当组件重新渲染时，重置所有子菜单的显示状态
  useEffect(() => {
    setSubMenuVisible({});
  }, [fileType, isFavorite]);

  const i18nText = {
    document: fm('File.document'),
    spreadsheet: fm('File.spreadsheet'),
    board: fm('File.board'),
    mindmap: fm('File.mindmap'),
    subscribeUpdate: fm('FileMenuPopover.subscribeUpdate'),
    subscribeUpdatePpt: fm('FileMenuPopover.subscribeUpdatePpt'),
    lockFile: fm('FileMenuPopover.lockFile'),
    saveTemplate: fm('FileMenuPopover.saveTemplate'),
    viewHistory: fm('FileMenuPopover.viewHistory'),
    saveVersion: fm('FileMenuPopover.saveVersion'),
    addComment: fm('FileMenuPopover.addComment'),
    viewComment: fm('FileMenuPopover.viewComment'),
    guide: fm('FileMenuPopover.guide'),
    shortcut: fm('FileMenuPopover.shortcut'),
    helpCenter: fm('FileMenuPopover.helpCenter'),
  };

  const i18n = {
    document: fm('useFileDetail.modocTitle'),
    documentPro: fm('useFileDetail.modocTitle'),
    spreadsheet: fm('useFileDetail.mosheetTitle'),
    table: fm('useFileDetail.tableTitle'),
    presentation: fm('useFileDetail.pptTitle'),
    form: fm('useFileDetail.formTitle'),
    mindmap: fm('useFileDetail.mindmapTitle'),
  };

  const fileAddToKnowledgeBaseConfigItem = createFileAddToKnowledgeBaseConfigItemForEditorHeader(
    permissionsAndReasons,
    fileType,
  );

  const SUPPORT_VIEW_CONFIG = [
    {
      key: 'move', // 移动
      title: fm('FileMenuPopover.move'),
      disabled: !permissionsAndReasons?.canRemove?.value,
      disabledTip: fm('FileMenuPopover.noMovePermissionTip'),
    },
    {
      key: 'createCopy', // 创建副本
      title: fm('FileMenuPopover.createCopy'),
      disabled: !permissionsAndReasons?.canDuplicate?.value,
      disabledTip: fm('FileMenuPopover.noCreateCopyPermissionTip'),
    },
    // 添加到知识库
    ...(fileAddToKnowledgeBaseConfigItem ? [fileAddToKnowledgeBaseConfigItem] : []),
    {
      key: 'fileInfo', // 文档信息
      title: fm('FileMenuPopover.documentInfo'),
      showDivider: true,
    },
    {
      key: 'delete', // 删除
      title: fm('FileMenuPopover.delete'),
    },
  ] satisfies MenuItem[];

  function getItems(type: string): MenuItem[] {
    const helpItemsMap = {
      document: [
        { key: 'guide', title: `${i18nText.document + i18nText.guide}` }, // 使用指南
        { key: 'shortcut', title: `${i18nText.document + i18nText.shortcut}` }, // 快捷键
        { key: 'helpCenter', title: `${i18nText.document + i18nText.helpCenter}` }, // 帮助中心
      ],
      spreadsheet: [
        { key: 'guide', title: `${i18nText.spreadsheet + i18nText.guide}` }, // 使用指南
        { key: 'shortcut', title: `${i18nText.spreadsheet + i18nText.shortcut}` }, // 快捷键
        { key: 'helpCenter', title: `${i18nText.spreadsheet + i18nText.helpCenter}` }, // 帮助中心
      ],
      board: [
        { key: 'guide', title: `${i18nText.board + i18nText.guide}`, noSupport: true, hide: true }, // 使用指南
        { key: 'shortcut', title: `${i18nText.board + i18nText.shortcut}` }, // 快捷键
        { key: 'helpCenter', title: `${i18nText.board + i18nText.helpCenter}` }, // 帮助中心
      ],
      mindmap: [
        { key: 'guide', title: `${i18nText.mindmap + i18nText.guide}` }, // 使用指南
        { key: 'shortcut', title: `${i18nText.mindmap + i18nText.shortcut}` }, // 快捷键
        { key: 'helpCenter', title: `${i18nText.mindmap + i18nText.helpCenter}` }, // 帮助中心
      ],
    };

    const commonItems: MenuItem[] = [
      {
        key: 'addToQuickAccess',
        title: fm('QuickAccess.add'),
        icon: quickAccessAdded ? <img src={checkOutlined} width={12} /> : null,
        active: quickAccessAdded,
      },
      ...(type !== FileIconType.Table && type !== FileIconType.Board
        ? [
            {
              key: 'subscribeUpdate',
              title: type !== FileIconType.Presentation ? i18nText.subscribeUpdate : i18nText.subscribeUpdatePpt,
              icon: subscribed ? <img src={checkOutlined} width={12} /> : null,
              active: subscribed,
              hide: isFeaturePoint(UserFeature.disable_update_subscription),
            },
          ]
        : []),
      {
        key: 'favorite',
        title: fm('FileMenuPopover.favorite'),
        icon: isFavorite ? <img src={checkOutlined} width={12} /> : null,
        showDivider: true,
        active: isFavorite,
      },
      {
        key: 'move',
        title: fm('FileMenuPopover.move'),
        disabled: !permissionsAndReasons?.canRemove?.value,
        disabledTip: fm('FileMenuPopover.noMovePermissionTip'),
      },
      {
        key: 'shortcut',
        title: fm('RightclickMouse.createShortcut'),
      },
      {
        key: 'lockFile',
        title: i18nText.lockFile,
        noSupport: true,
        hide: true,
      },
      {
        key: 'createCopy',
        title: fm('FileMenuPopover.createCopy'),
        showDivider: type === 'table' || type === 'form' || type === 'table',
        disabled: !permissionsAndReasons?.canDuplicate?.value,
        disabledTip: fm('FileMenuPopover.noCreateCopyPermissionTip'),
      },
      // 添加到知识库
      ...(fileAddToKnowledgeBaseConfigItem ? [fileAddToKnowledgeBaseConfigItem] : []),
    ];

    // 剔除'锁定文件'
    const commonitems_board = commonItems.filter((item) => item.key !== 'lockFile');

    switch (type) {
      case 'document':
      case 'documentPro':
      case 'spreadsheet': {
        const downItemsMap = {
          document: [
            { key: 'downImage', exportType: 'jpg', title: fm('FileMenuPopover.downImage') },
            { key: 'downWord', exportType: 'docx', title: fm('FileMenuPopover.downWord') },
            { key: 'downPDF', exportType: 'pdf', title: fm('FileMenuPopover.downPDF') },
            { key: 'downMarkdown', exportType: 'md', title: fm('FileMenuPopover.downMarkdown') },
          ],
          documentPro: [
            { key: 'downWord', exportType: 'docx', title: fm('FileMenuPopover.downWord') },
            { key: 'downPDF', exportType: 'pdf', title: fm('FileMenuPopover.downPDF') },
            { key: 'downWPS', exportType: 'wps', title: fm('FileMenuPopover.downWPS') },
            { key: 'downImagePDF', exportType: 'imagePdf', title: fm('FileMenuPopover.downImagePDF') },
            { key: 'downImage', exportType: 'image', title: fm('FileMenuPopover.downImage') },
          ],
          spreadsheet: [
            { key: 'downExcel', exportType: 'xlsx', title: fm('FileMenuPopover.downExcel') },
            {
              key: 'downPDF',
              exportType: 'pdf',
              title: fm('FileMenuPopover.downPDF'),
            },
            {
              key: 'downImage',
              exportType: 'image',
              title: fm('FileMenuPopover.downImage'),
            },
          ],
        };

        const downItems = downItemsMap[type];

        const configs = [
          ...commonItems,
          {
            key: 'download',
            title: fm('FileMenuPopover.download'),
            disabled: !canDownload,
            disabledTip: fm('FileMenuPopover.noDownloadPermissionTip'),
            subItems: downItems,
          },
          {
            key: 'print',
            title: fm('FileMenuPopover.print'),
            disabled: !permissionsAndReasons?.canPrint?.value,
            disabledTip: fm('FileMenuPopover.noPrintPermissionTip'),
            showDivider: true,
          },
          {
            key: 'saveTemplate',
            title: i18nText.saveTemplate,
            disabled: !permissionsAndReasons?.canSaveAsTemplate?.value,
            disabledTip: fm('FileMenuPopover.noSaveAsTemplatePermissionTip'),
          }, //保存模板
          {
            key: 'saveVersion',
            title: i18nText.saveVersion,
            showDivider: true,
          },
          {
            key: 'viewHistory',
            title: i18nText.viewHistory,
          },
          ...(type === FileIconType.Doc
            ? [
                {
                  key: 'show',
                  title: fm('FileMenuPopover.show'),
                  subItems: [
                    {
                      key: 'comment',
                      exportType: 'comment',
                      title: fm('FileMenuPopover.showCommentCards'),
                      active: showComment,
                      icon: showComment ? <img src={checkOutlined} width={12} /> : null,
                    },
                    {
                      key: 'menu',
                      exportType: 'menu',
                      title: fm('FileMenuPopover.showMenu'),
                      active: showMenu,
                      icon: showMenu ? <img src={checkOutlined} width={12} /> : null,
                    },
                    {
                      key: 'writer',
                      exportType: 'writer',
                      title: fm('FileMenuPopover.showWriter'),
                      active: showWriter,
                      icon: showWriter ? <img src={checkOutlined} width={12} /> : null,
                    },
                  ],
                  showDivider: true,
                },
              ]
            : []), // 显示/隐藏
          ...(type !== 'documentPro' ? [{ key: 'viewCommentList', title: fm('FileMenuPopover.viewCommentList') }] : []),
          ...(type === FileIconType.Mosheet
            ? [{ key: 'viewLockMosheet', title: fm('FileMenuPopover.viewLockMosheet'), hide: true }]
            : []), //查看锁定工作表
          { key: 'fileInfo', title: i18n[type], showDivider: type === 'documentPro' },

          // 表格、轻文档、白板、思维导图
          ['spreadsheet', 'document', 'board', 'mindmap'].includes(type)
            ? {
                key: 'help',
                title: `${fm('FileMenuPopover.help')}`,
                subItems: helpItemsMap[type as keyof typeof helpItemsMap],
                showDivider: !isPublicShare,
              }
            : { key: 'help', title: fm('FileMenuPopover.help'), hide: true, showDivider: true },
          { key: 'delete', title: fm('FileMenuPopover.delete') },
        ];
        const unCollaboratorConfigs = [
          'addToQuickAccess',
          'subscribeUpdate',
          'favorite',
          'move',
          'shortcut',
          'lockFile',
          'createCopy',
          'download',
          'print',
          'help',
        ];
        return !isPublicShare ? configs : getUnCollaborator(configs, unCollaboratorConfigs);
      }
      case 'table': {
        const configs = [
          ...commonItems,
          // { key: 'convertToMoSheet', title: fm('FileMenuPopover.convertToMoSheet') },
          {
            key: 'downToExcel',
            title: fm('FileMenuPopover.downToExcel'),
            showDivider: !isPublicShare,
            // table只有石墨有，用canExport来判断
            disabled: !permissionsAndReasons?.canExport?.value,
          },
          {
            key: 'saveTemplate',
            title: i18nText.saveTemplate,
            disabled: !permissionsAndReasons?.canSaveAsTemplate?.value,
            disabledTip: fm('FileMenuPopover.noSaveAsTemplatePermissionTip'),
          }, //保存模板
          { key: 'fileInfo', title: i18n[type] },
          { key: 'tableHelp', title: fm('FileMenuPopover.tableHelp'), noSupport: true, hide: true },
          { key: 'delete', title: fm('FileMenuPopover.delete') },
        ];
        const unCollaboratorConfigs = [
          'addToQuickAccess',
          'subscribeUpdate',
          'favorite',
          'move',
          'shortcut',
          'lockFile',
          'createCopy',
          'downToExcel',
          'print',
          'helpCenter',
        ];
        return !isPublicShare ? configs : getUnCollaborator(configs, unCollaboratorConfigs);
      }
      case 'presentation': {
        const configs = [
          ...commonItems,
          {
            key: 'download',
            title: fm('FileMenuPopover.download'),
            subItems: [
              { key: 'downPPTX', exportType: 'pptx', title: fm('FileMenuPopover.downPPTX') },
              { key: 'downPDF', exportType: 'pdf', title: fm('FileMenuPopover.downPDF') },
              {
                key: 'downImagePDF',
                exportType: 'imagePdf',
                title: fm('FileMenuPopover.downImagePDF'),
              },
              { key: 'downImage', exportType: 'image', title: fm('FileMenuPopover.downImage') },
            ],
            disabled: !canDownload,
            disabledTip: fm('FileMenuPopover.noDownloadPermissionTip'),
          },
          {
            key: 'print',
            title: fm('FileMenuPopover.print'),
            disabled: !permissionsAndReasons?.canPrint?.value,
            disabledTip: fm('FileMenuPopover.noPrintPermissionTip'),
            showDivider: true,
          },
          {
            key: 'saveVersion',
            title: i18nText.saveVersion,
            disabled: !isCollaborators,
          },
          {
            key: 'saveTemplate',
            title: i18nText.saveTemplate,
            showDivider: true,
            disabled: !permissionsAndReasons?.canSaveAsTemplate?.value,
            disabledTip: fm('FileMenuPopover.noSaveAsTemplatePermissionTip'),
          },
          {
            key: 'viewHistory',
            title: fm('FileMenuPopover.viewHistory'),
            disabled: !isCollaborators,
          },
          {
            key: 'addComment',
            title: i18nText.addComment,
            disabled: !isCollaborators,
          },
          {
            key: 'viewComment',
            title: i18nText.viewComment,
            showDivider: isCollaborators,
            disabled: !isCollaborators,
            active: showComment && isCollaborators,
            icon: showComment && isCollaborators ? <img src={checkOutlined} width={12} /> : null,
          },
          { key: 'fileInfo', title: i18n[type], showDivider: true },
          { key: 'delete', title: fm('FileMenuPopover.delete') },
        ];
        const unCollaboratorConfigs = [
          'addToQuickAccess',
          'subscribeUpdate',
          'favorite',
          'move',
          'shortcut',
          'saveVersion',
          'viewHistory',
          'addComment',
          'viewComment',
        ];
        return !isPublicShare ? configs : getUnCollaborator(configs, unCollaboratorConfigs);
      }
      case 'form': {
        const configs = [
          ...commonItems,
          {
            key: 'saveTemplate',
            title: i18nText.saveTemplate,
            showDivider: true,
            disabled: !permissionsAndReasons?.canSaveAsTemplate?.value,
            disabledTip: fm('FileMenuPopover.noSaveAsTemplatePermissionTip'),
          }, //保存模板
          { key: 'formHelp', title: fm('FileMenuPopover.formHelp'), showDivider: true, noSupport: true, hide: true },
          { key: 'delete', title: fm('FileMenuPopover.delete') },
        ];
        const unCollaboratorConfigs = [
          'addToQuickAccess',
          'subscribeUpdate',
          'favorite',
          'move',
          'shortcut',
          'createCopy',
        ];
        return !isPublicShare ? configs : getUnCollaborator(configs, unCollaboratorConfigs);
      }
      case 'board': {
        const configs = [
          ...commonitems_board,
          {
            key: 'fileInfo', // 文档信息
            title: fm('FileMenuPopover.whiteboardInfo'),
          },
          {
            key: 'help',
            title: `${fm('FileMenuPopover.help')}`,
            subItems: helpItemsMap[type as keyof typeof helpItemsMap],
            showDivider: true,
          },
          {
            key: 'delete', // 删除
            title: fm('FileMenuPopover.delete'),
          },
        ];
        const unCollaboratorConfigs = ['addToQuickAccess', 'favorite', 'move', 'shortcut', 'createCopy'];
        return !isPublicShare ? configs : getUnCollaborator(configs, unCollaboratorConfigs);
      }
      case 'mindmap': {
        const configs = [
          ...commonItems,
          {
            key: 'download',
            title: fm('FileMenuPopover.download'),
            subItems: [
              { key: 'xmind', exportType: 'xmind', title: fm('FileMenuPopover.xmind') },
              { key: 'downImage', exportType: 'jpg', title: fm('FileMenuPopover.downImage') },
            ],
            disabled: !canDownload,
            disabledTip: fm('FileMenuPopover.noDownloadPermissionTip'),
            showDivider: true,
          },
          { key: 'viewHistory', title: fm('FileMenuPopover.viewHistory'), showDivider: true },
          { key: 'fileInfo', title: i18n[type], showDivider: type === 'mindmap' },
          {
            key: 'help',
            title: `${fm('FileMenuPopover.help')}`,
            subItems: helpItemsMap[type as keyof typeof helpItemsMap],
            showDivider: true,
          },
          { key: 'delete', title: fm('FileMenuPopover.delete') },
        ];
        const unCollaboratorConfigs = ['addToQuickAccess', 'favorite', 'move', 'shortcut', 'createCopy', 'download'];
        return !isPublicShare ? configs : getUnCollaborator(configs, unCollaboratorConfigs);
      }

      default:
        return !isPublicShare ? SUPPORT_VIEW_CONFIG : getUnCollaborator(SUPPORT_VIEW_CONFIG);
    }
  }

  const items = getItems(fileType);

  function handleItemClick(item: MenuItem) {
    // 点击触发时同步关闭所有子菜单弹框
    setSubMenuVisible({});
    onItemClick?.({ key: item.key, exportType: item.exportType || '', noSupport: item.noSupport });
  }

  // 控制子菜单显示状态
  function subMenuVisibleChange(key: string, visible: boolean) {
    setSubMenuVisible((prev) => ({
      ...prev,
      [key]: visible,
    }));
  }

  // 通用菜单项渲染函数
  function renderMenuItem(item: MenuItem) {
    // 判断是否应该禁用该菜单项
    const isDisabled = item.disabled;

    // 创建菜单项内容
    const menuItemContent = (
      <Tooltip placement="left" title={isDisabled ? item.disabledTip : ''}>
        <div
          key={item.key}
          className={classNames(css.item, {
            [css.danger]: item.key === 'delete',
            [css.disabled]: isDisabled,
            [css.active]: item.active,
          })}
          onClick={() => !isDisabled && handleItemClick(item)}
        >
          <div className={css.itemContent}>
            <span>{item.title}</span>
            {item.icon && <span className={css.itemIcon}>{item.icon}</span>}
          </div>
          {item.subItems && <img src={arrowRight} width={5.5} />}
        </div>
      </Tooltip>
    );

    // 如果有子菜单，则渲染弹出菜单
    if (item.subItems && item.subItems.length) {
      return (
        <React.Fragment key={item.key}>
          <Popover
            key={item.key}
            arrow={false}
            content={
              <div className={css.submenuContent}>
                {item.subItems.map((subItem) => (
                  <React.Fragment key={subItem.key}>
                    {renderMenuItem({ ...subItem, key: `${item.key}-${subItem.key}` })}
                  </React.Fragment>
                ))}
              </div>
            }
            open={subMenuVisible[item.key]}
            placement="rightTop"
            trigger={isDisabled ? [] : ['hover']}
            onOpenChange={(visible) => subMenuVisibleChange(item.key, visible)}
          >
            {!item.hide && menuItemContent}
          </Popover>
          {!item.hide && item.showDivider && <div className={css.divider} />}
        </React.Fragment>
      );
    }

    // 无子菜单，直接返回菜单项
    return (
      <React.Fragment key={item.key}>
        {!item.hide && menuItemContent}
        {!item.hide && item.showDivider && <div className={css.divider} />}
      </React.Fragment>
    );
  }

  return (
    <div className={css.container}>
      <div className={css.section}>{items.map(renderMenuItem)}</div>
    </div>
  );
}
